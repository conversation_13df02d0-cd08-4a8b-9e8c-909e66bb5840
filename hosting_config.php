<?php
/**
 * Hosting Configuration for cyberwolf.wuaze.com
 * This file contains hosting-specific settings and checks
 */

// Domain configuration
define('DOMAIN', 'cyberwolf.wuaze.com');
define('BASE_URL', 'https://' . DOMAIN);
define('LAB_PATH', '/labs/');

// Database configuration for hosting environment
class HostingConfig {
    
    // Database settings - Update these for your hosting provider
    public static $db_config = [
        'host' => 'localhost',  // Usually localhost for shared hosting
        'username' => 'cyberwolf_user',  // Your hosting DB username
        'password' => 'your_db_password',  // Your hosting DB password
        'database' => 'cyberwolf_labs'  // Your hosting database name
    ];
    
    // Check if we're running on the correct domain
    public static function validateDomain() {
        $current_domain = $_SERVER['HTTP_HOST'] ?? '';
        if ($current_domain !== DOMAIN && $current_domain !== 'www.' . DOMAIN) {
            return false;
        }
        return true;
    }
    
    // Get database connection for hosting environment
    public static function getDatabaseConnection() {
        $config = self::$db_config;
        
        $conn = @mysqli_connect(
            $config['host'],
            $config['username'],
            $config['password'],
            $config['database']
        );
        
        if (!$conn) {
            error_log("Database connection failed: " . mysqli_connect_error());
            return false;
        }
        
        return $conn;
    }
    
    // Check hosting environment requirements
    public static function checkHostingRequirements() {
        $requirements = [
            'php_version' => version_compare(PHP_VERSION, '7.0.0', '>='),
            'mysqli_extension' => extension_loaded('mysqli'),
            'xml_extension' => extension_loaded('xml'),
            'curl_extension' => extension_loaded('curl'),
            'file_uploads' => ini_get('file_uploads'),
            'writable_upload_dir' => is_writable(__DIR__ . '/upload/userfiles/'),
            'writable_xxe_dir' => is_writable(__DIR__ . '/xxe/files/')
        ];
        
        return $requirements;
    }
    
    // Generate secure base URL for labs
    public static function getLabUrl($lab_name) {
        return BASE_URL . LAB_PATH . $lab_name . '/';
    }
    
    // Security headers for hosting environment
    public static function setSecurityHeaders() {
        // Only set headers if not already sent
        if (!headers_sent()) {
            header('X-Content-Type-Options: nosniff');
            header('X-Frame-Options: SAMEORIGIN');  // Allow framing for lab functionality
            header('X-XSS-Protection: 0');  // Disable for XSS labs to work
            header('Referrer-Policy: strict-origin-when-cross-origin');
        }
    }
    
    // Create .htaccess for hosting environment
    public static function createHtaccess() {
        $htaccess_content = "# CyberWolf Labs - Hosting Configuration\n";
        $htaccess_content .= "RewriteEngine On\n\n";
        
        // Force HTTPS
        $htaccess_content .= "# Force HTTPS\n";
        $htaccess_content .= "RewriteCond %{HTTPS} off\n";
        $htaccess_content .= "RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]\n\n";
        
        // Error pages
        $htaccess_content .= "# Custom Error Pages\n";
        $htaccess_content .= "ErrorDocument 404 /labs/404.html\n";
        $htaccess_content .= "ErrorDocument 500 /labs/500.html\n\n";
        
        // Security settings
        $htaccess_content .= "# Security Settings\n";
        $htaccess_content .= "ServerTokens Prod\n";
        $htaccess_content .= "ServerSignature Off\n\n";
        
        // File protection
        $htaccess_content .= "# Protect sensitive files\n";
        $htaccess_content .= "<Files \"database_setup.sql\">\n";
        $htaccess_content .= "    Order Allow,Deny\n";
        $htaccess_content .= "    Deny from all\n";
        $htaccess_content .= "</Files>\n\n";
        
        $htaccess_content .= "<Files \"hosting_config.php\">\n";
        $htaccess_content .= "    Order Allow,Deny\n";
        $htaccess_content .= "    Deny from all\n";
        $htaccess_content .= "</Files>\n\n";
        
        // PHP settings for labs
        $htaccess_content .= "# PHP Settings for Labs\n";
        $htaccess_content .= "php_value upload_max_filesize 10M\n";
        $htaccess_content .= "php_value post_max_size 10M\n";
        $htaccess_content .= "php_value max_execution_time 30\n";
        $htaccess_content .= "php_value memory_limit 128M\n\n";
        
        // Directory browsing
        $htaccess_content .= "# Disable directory browsing\n";
        $htaccess_content .= "Options -Indexes\n\n";
        
        return file_put_contents(__DIR__ . '/.htaccess', $htaccess_content);
    }
}

// Auto-run hosting setup if accessed directly
if (basename($_SERVER['PHP_SELF']) === 'hosting_config.php') {
    echo "🌐 CyberWolf Labs - Hosting Configuration\n";
    echo "========================================\n\n";
    
    // Check domain
    if (HostingConfig::validateDomain()) {
        echo "✅ Domain validation passed: " . DOMAIN . "\n";
    } else {
        echo "⚠️  Warning: Running on different domain: " . ($_SERVER['HTTP_HOST'] ?? 'unknown') . "\n";
    }
    
    // Check requirements
    echo "\n📋 Checking hosting requirements:\n";
    $requirements = HostingConfig::checkHostingRequirements();
    
    foreach ($requirements as $requirement => $status) {
        $icon = $status ? "✅" : "❌";
        echo "$icon $requirement: " . ($status ? "OK" : "FAILED") . "\n";
    }
    
    // Test database connection
    echo "\n📊 Testing database connection:\n";
    $conn = HostingConfig::getDatabaseConnection();
    if ($conn) {
        echo "✅ Database connection successful\n";
        mysqli_close($conn);
    } else {
        echo "❌ Database connection failed\n";
        echo "💡 Please update database credentials in hosting_config.php\n";
    }
    
    // Create .htaccess
    echo "\n🔧 Creating .htaccess file:\n";
    if (HostingConfig::createHtaccess()) {
        echo "✅ .htaccess file created successfully\n";
    } else {
        echo "❌ Failed to create .htaccess file\n";
    }
    
    echo "\n🚀 Hosting setup completed!\n";
    echo "🔗 Access your labs at: " . BASE_URL . LAB_PATH . "\n";
}
?>

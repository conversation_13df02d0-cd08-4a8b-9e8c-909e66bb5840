<?php
session_start();
include 'fun.php';
$msg = "";
if(isset($_POST['url'])){
    $url = $_POST['url'];
    if(!strpos($url, ".json")){
        $msg = "Not allowed extension, try (json) extension.";
    }else{
        $urlSplit = explode("/", $url);
        $file = $urlSplit[count($urlSplit)-1];
        get($url, $file);
    }
}

?>
<html>
  <head>
    <title>Add Pages - CyberWolf Training Class</title>
    <script src="https://code.angularjs.org/1.1.5/angular.min.js"></script>
    <link rel="stylesheet" type="text/css" href="../style/css.css">
    <style>
        .cyberwolf-banner {
            background: linear-gradient(45deg, #1a1a1a, #2d2d2d);
            color: #00ff00;
            text-align: center;
            padding: 15px;
            border: 2px solid #00ff00;
            margin: 10px;
            border-radius: 5px;
        }
        .training-info {
            background: rgba(0, 255, 0, 0.1);
            border: 1px solid #00ff00;
            padding: 15px;
            margin: 20px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
        }
        .lab-objective {
            background: #1a1a1a;
            border-left: 4px solid #00ff00;
            padding: 10px;
            margin: 10px 0;
        }
    </style>
  </head>
  <body id="bodyId">
    <div class="cyberwolf-banner">
        <h2>🐺 CyberWolf Training Class - File Inclusion Lab 🐺</h2>
        <p>Add Pages Functionality | Developed for CyberWolf Training</p>
        <p style="font-size: 0.9em;">Domain: cyberwolf.wuaze.com</p>
    </div>

    <div class="header">
      <a href="index.php" class="logo">Books Library</a>
      <div class="header-right">
        <a href="index.php">Home</a>
        <a href="profile.php">Profile</a>
        <a class="active" href="add.php">Add Pages</a>
        <a href="login.php">Login</a>
      </div>
    </div>
    <div class="training-info">
        <div class="lab-objective">
            <h3>🎯 Lab Objective: Add Pages Functionality</h3>
            <p>This lab demonstrates file inclusion vulnerabilities through a "Add Pages" feature that allows users to fetch external content.</p>
        </div>

        <div class="lab-objective">
            <h3>📚 Learning Goals:</h3>
            <ul>
                <li>Understand remote file inclusion (RFI) vulnerabilities</li>
                <li>Learn about file extension filtering bypasses</li>
                <li>Practice exploiting URL-based file inclusion</li>
                <li>Explore server-side request forgery (SSRF) concepts</li>
            </ul>
        </div>

        <div class="lab-objective">
            <h3>🔍 Exploitation Hints:</h3>
            <ul>
                <li>The system only allows .json files - can you bypass this?</li>
                <li>Try using URL fragments or query parameters</li>
                <li>Consider using data:// or php:// wrappers</li>
                <li>Test with local file paths</li>
            </ul>
        </div>
    </div>

    <div>
        <center>
            <h3>📄 Add New Page Content</h3>
            <p style="color: #666;">Enter a URL to fetch content (JSON files only)</p>
            <form method="post">
                <input id="email" type="url" name="url" autocomplete="off" placeholder="Enter URL to JSON file" style="width: 400px; padding: 10px; margin: 10px;"><br>
                <input id="send" type="submit" value="Fetch Content" style="padding: 10px 20px; margin: 10px;">
            </form>
            <?php if($msg != ""): ?>
                <div style="background: #ffeeee; border: 1px solid #ff0000; padding: 10px; margin: 10px; border-radius: 5px; color: #cc0000;">
                    <strong>Error:</strong> <?php echo htmlspecialchars($msg); ?>
                </div>
            <?php endif; ?>

            <div style="margin: 20px; padding: 15px; background: #f0f0f0; border-radius: 5px;">
                <h4>🧪 Test Examples:</h4>
                <p><strong>Valid:</strong> https://jsonplaceholder.typicode.com/posts/1.json</p>
                <p><strong>Challenge:</strong> Try to include local files or bypass the .json restriction</p>
            </div>
        </center>
    </div>

    <div class="cyberwolf-banner" style="margin-top: 30px;">
        <p>🎓 This lab is part of the CyberWolf Training Class curriculum</p>
        <p>🔗 Visit: <a href="https://cyberwolf.wuaze.com" style="color: #00ffff;">cyberwolf.wuaze.com</a> | 📖 <a href="../answers.html" style="color: #00ffff;">View Solutions</a></p>
    </div>
  </body>
</html>
<?php
session_start();
include 'fun.php';

$f = @$_GET['f'];
$blocked = array('../');

if( !in_array($f, $blocked) ){
    getFile($f);
}

// Delete lines fomr 5 to 10
?>
<html>
  <head>
    <title>File Inclusion Lab - CyberWolf Training Class</title>
    <script src="https://code.angularjs.org/1.1.5/angular.min.js"></script>
    <link rel="stylesheet" type="text/css" href="../style/css.css">
    <style>
        .cyberwolf-banner {
            background: linear-gradient(45deg, #1a1a1a, #2d2d2d);
            color: #00ff00;
            text-align: center;
            padding: 15px;
            border: 2px solid #00ff00;
            margin: 10px;
            border-radius: 5px;
        }
        .training-notice {
            background: rgba(0, 255, 0, 0.1);
            border: 1px solid #00ff00;
            padding: 10px;
            margin: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }
    </style>
  </head>
  <body id="bodyId">
    <div class="cyberwolf-banner">
        <h2>🐺 CyberWolf Training Class - File Inclusion Lab 🐺</h2>
        <p>The lab developed for CyberWolf Training Class</p>
        <p style="font-size: 0.9em;">Domain: cyberwolf.wuaze.com</p>
    </div>

    <div class="training-notice">
        <strong>📚 Lab Purpose:</strong> This lab demonstrates Local File Inclusion (LFI) vulnerabilities through a vulnerable file inclusion mechanism.
        <br><strong>🎯 Objective:</strong> Learn to exploit file inclusion vulnerabilities and understand their security implications.
    </div>

    <div class="header">
      <a href="index.php" class="logo">Books Library</a>
      <div class="header-right">
        <a class="active" href="index.php">Home</a>
        <a href="profile.php">Profile</a>
        <a href="add.php">Add Pages</a>
        <a href="login.php">Login</a>
      </div>
    </div>
    <div>
        <center>
            <br><br><br><h3 id="type"></h3>
        </center>
    </div>
    <script type="text/javascript">
        var msg = "Welcome.",
            i=0,
            body=document.getElementById('bodyId');
        body.onload = function(){
            'use strict';
            var typeWriter = setInterval(function(){

                document.getElementById('type').innerHTML += msg[i];
                i = i + 1;

                if(i>msg.length-1){
                    clearInterval(typeWriter)
                }
            }, 100);
        }
    </script>
  </body>
</html>
{"title": "CyberWolf Training Class - <PERSON><PERSON> Page", "description": "This is a sample JSON file for the Add Pages functionality", "content": {"heading": "Welcome to CyberWolf Training", "body": "This lab demonstrates file inclusion vulnerabilities through the Add Pages feature.", "training_info": {"platform": "CyberWolf Training Class", "domain": "cyberwolf.wuaze.com", "lab_type": "File Inclusion (LFI/RFI)", "difficulty": "Intermediate"}, "learning_objectives": ["Understand file inclusion vulnerabilities", "Learn about extension filtering bypasses", "Practice remote file inclusion attacks", "Explore server-side request forgery"], "exploitation_techniques": ["Directory traversal (../../../etc/passwd)", "PHP wrapper usage (php://filter/)", "Data URI schemes (data://text/plain,)", "Log poisoning attacks", "Extension bypass methods"]}, "security_notice": "This lab contains intentionally vulnerable code for educational purposes only. Use only in authorized testing environments.", "created_by": "CyberWolf Training Team", "version": "1.0"}
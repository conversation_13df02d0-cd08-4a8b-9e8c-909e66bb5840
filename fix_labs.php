<?php
/**
 * Lab Error Fixing Script
 * This script fixes common issues in all web penetration testing labs
 */

echo "🔧 Starting Lab Error Fixing Process...\n\n";

// Database connection test
function testDatabaseConnection() {
    echo "📊 Testing database connection...\n";
    
    $conn = @mysqli_connect('localhost', 'root', '', 'test');
    if (!$conn) {
        echo "❌ Database connection failed: " . mysqli_connect_error() . "\n";
        echo "💡 Please run: mysql -u root -p < database_setup.sql\n\n";
        return false;
    } else {
        echo "✅ Database connection successful\n\n";
        mysqli_close($conn);
        return true;
    }
}

// Check and create required directories
function createRequiredDirectories() {
    echo "📁 Creating required directories...\n";
    
    $directories = [
        'upload/userfiles',
        'xxe/files',
        'file inclusion/files'
    ];
    
    foreach ($directories as $dir) {
        if (!is_dir($dir)) {
            if (mkdir($dir, 0755, true)) {
                echo "✅ Created directory: $dir\n";
            } else {
                echo "❌ Failed to create directory: $dir\n";
            }
        } else {
            echo "✅ Directory exists: $dir\n";
        }
    }
    echo "\n";
}

// Fix file permissions
function fixFilePermissions() {
    echo "🔐 Fixing file permissions...\n";
    
    $writableDirectories = [
        'upload/userfiles',
        'xxe/files',
        'file inclusion/files'
    ];
    
    foreach ($writableDirectories as $dir) {
        if (is_dir($dir)) {
            chmod($dir, 0755);
            echo "✅ Set permissions for: $dir\n";
        }
    }
    echo "\n";
}

// Check PHP configuration
function checkPHPConfiguration() {
    echo "🐘 Checking PHP configuration...\n";
    
    // Check file uploads
    if (ini_get('file_uploads')) {
        echo "✅ File uploads enabled\n";
    } else {
        echo "❌ File uploads disabled\n";
    }
    
    // Check upload max filesize
    echo "📏 Max upload size: " . ini_get('upload_max_filesize') . "\n";
    
    // Check post max size
    echo "📏 Max POST size: " . ini_get('post_max_size') . "\n";
    
    // Check allow_url_include for LFI lab
    if (ini_get('allow_url_include')) {
        echo "⚠️  allow_url_include is enabled (good for LFI lab)\n";
    } else {
        echo "ℹ️  allow_url_include is disabled\n";
    }
    
    echo "\n";
}

// Validate lab files
function validateLabFiles() {
    echo "📋 Validating lab files...\n";
    
    $requiredFiles = [
        'sql/login.php',
        'sql/index.php',
        'xss/login.php',
        'xss/contact.php',
        'xss/search.php',
        'csrf/login.php',
        'csrf/settings.php',
        'idor/login.php',
        'idor/profile.php',
        'upload/login.php',
        'upload/profile.php',
        'file inclusion/index.php',
        'file inclusion/fun.php',
        'xxe/login.php',
        'xxe/check.php',
        'cors/login.php',
        'insecuredes/login.php',
        'insecuredes/classes.php',
        'jsonp/index.php',
        'postmessage/index.php'
    ];
    
    $missingFiles = [];
    foreach ($requiredFiles as $file) {
        if (!file_exists($file)) {
            $missingFiles[] = $file;
            echo "❌ Missing file: $file\n";
        } else {
            echo "✅ Found file: $file\n";
        }
    }
    
    if (empty($missingFiles)) {
        echo "✅ All required files present\n";
    } else {
        echo "⚠️  " . count($missingFiles) . " files missing\n";
    }
    echo "\n";
}

// Create .htaccess for security (optional)
function createHtaccess() {
    echo "🛡️  Creating security .htaccess files...\n";
    
    $htaccessContent = "# Security headers\n";
    $htaccessContent .= "Header always set X-Content-Type-Options nosniff\n";
    $htaccessContent .= "Header always set X-Frame-Options DENY\n";
    $htaccessContent .= "Header always set X-XSS-Protection \"1; mode=block\"\n";
    
    // Don't create .htaccess in root as it might interfere with labs
    echo "ℹ️  Skipping .htaccess creation to avoid interfering with lab functionality\n\n";
}

// Main execution
function main() {
    echo "🚀 Web Penetration Testing Labs - Error Fixing Tool\n";
    echo "==================================================\n\n";
    
    // Run all checks and fixes
    testDatabaseConnection();
    createRequiredDirectories();
    fixFilePermissions();
    checkPHPConfiguration();
    validateLabFiles();
    createHtaccess();
    
    echo "🎉 Lab fixing process completed!\n\n";
    
    echo "📝 Next Steps:\n";
    echo "1. Ensure Apache/Nginx and MySQL are running\n";
    echo "2. Import database_setup.sql if database connection failed\n";
    echo "3. Test each lab individually\n";
    echo "4. Check answers.html for exploitation techniques\n\n";
    
    echo "🔗 Access labs at: http://localhost/labs/\n";
    echo "📖 View answers at: http://localhost/labs/answers.html\n\n";
}

// Run the script
main();
?>

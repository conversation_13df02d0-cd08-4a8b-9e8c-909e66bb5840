{"course_info": {"title": "CyberWolf Training Class - Advanced Web Security", "instructor": "CyberWolf Security Team", "platform": "cyberwolf.wuaze.com", "lab_category": "File Inclusion Vulnerabilities"}, "module_content": {"introduction": "Welcome to the File Inclusion module of CyberWolf Training Class. This hands-on lab will teach you about Local File Inclusion (LFI) and Remote File Inclusion (RFI) vulnerabilities.", "topics_covered": [{"topic": "Local File Inclusion (LFI)", "description": "Learn how applications include local files and how this can be exploited", "techniques": ["Directory traversal", "Null byte injection", "Path truncation"]}, {"topic": "Remote File Inclusion (RFI)", "description": "Understand how remote files can be included and executed", "techniques": ["Remote code execution", "Malicious file hosting", "Wrapper exploitation"]}, {"topic": "Filter Bypasses", "description": "Methods to bypass file extension and path filtering", "techniques": ["Double extensions", "Case manipulation", "URL encoding"]}, {"topic": "Log Poisoning", "description": "Technique to inject malicious code into log files", "techniques": ["User-Agent poisoning", "Access log manipulation", "Error log injection"]}]}, "practical_exercises": {"exercise_1": {"name": "Basic Directory Traversal", "objective": "Use ../ sequences to access system files", "target_file": "/etc/passwd", "hint": "Try using multiple ../ sequences"}, "exercise_2": {"name": "PHP Wrapper Exploitation", "objective": "Use PHP wrappers to read file contents", "example": "php://filter/convert.base64-encode/resource=", "hint": "Combine with local file paths"}, "exercise_3": {"name": "Extension Filter Bypass", "objective": "Bypass the .json extension requirement", "techniques": ["URL fragments", "Query parameters", "Null bytes"], "hint": "Try appending #.json or ?.json"}}, "security_implications": {"impact": "File inclusion vulnerabilities can lead to:", "consequences": ["Sensitive file disclosure", "Remote code execution", "Server compromise", "Data exfiltration", "Privilege escalation"]}, "prevention_measures": {"best_practices": ["Input validation and sanitization", "Whitelist allowed files", "Use absolute paths", "Disable dangerous PHP functions", "Implement proper access controls"]}, "lab_metadata": {"developed_for": "CyberWolf Training Class", "difficulty_level": "Intermediate to Advanced", "estimated_time": "45-60 minutes", "prerequisites": "Basic understanding of web applications and PHP", "tools_needed": ["Web browser", "Burp Suite or similar proxy", "Text editor"]}}
# Web Penetration Testing Labs - CyberWolf

## 🎯 Overview
Complete collection of web application security testing labs covering the most common vulnerabilities found in modern web applications.

## 🚀 Quick Start

### 1. Database Setup
```bash
mysql -u root -p < database_setup.sql
```

### 2. Fix Common Issues
```bash
php fix_labs.php
```

### 3. Access Labs
- **Main Dashboard**: `http://localhost/labs/index.php`
- **Lab Answers**: `http://localhost/labs/answers.html`

## 🧪 Available Labs

| Lab | Vulnerability Type | Difficulty | Key Learning |
|-----|-------------------|------------|--------------|
| **SQL** | SQL Injection | ⭐⭐ | Database attacks, authentication bypass |
| **XSS** | Cross-Site Scripting | ⭐⭐⭐ | Client-side attacks, filter bypasses |
| **CSRF** | Cross-Site Request Forgery | ⭐⭐ | State-changing attacks |
| **IDOR** | Insecure Direct Object Reference | ⭐⭐ | Access control flaws |
| **Upload** | File Upload Vulnerabilities | ⭐⭐⭐ | File handling, RCE |
| **LFI** | Local File Inclusion | ⭐⭐⭐ | Path traversal, log poisoning |
| **XXE** | XML External Entity | ⭐⭐⭐⭐ | XML parsing, SSRF |
| **CORS** | Cross-Origin Resource Sharing | ⭐⭐ | Same-origin policy bypass |
| **Deserial** | Insecure Deserialization | ⭐⭐⭐⭐ | Object injection, RCE |
| **JSONP** | JSONP Vulnerabilities | ⭐⭐ | Callback manipulation |
| **PostMsg** | PostMessage Vulnerabilities | ⭐⭐⭐ | Cross-frame communication |

## 🔧 Prerequisites

### Required Software
- **Web Server**: Apache/Nginx
- **PHP**: Version 7.0+
- **Database**: MySQL/MariaDB
- **Browser**: Modern browser with developer tools

### PHP Extensions
- `mysqli` - Database connectivity
- `xml` - XML processing (for XXE lab)
- `curl` - HTTP requests (for some labs)

## 📁 Directory Structure
```
labs/
├── index.php              # Main dashboard
├── answers.html           # Complete solutions
├── database_setup.sql     # Database initialization
├── fix_labs.php          # Error fixing script
├── sql/                  # SQL Injection lab
├── xss/                  # XSS lab with filter bypasses
├── csrf/                 # CSRF lab
├── idor/                 # IDOR lab
├── upload/               # File upload lab
├── file inclusion/       # LFI lab
├── xxe/                  # XXE lab
├── cors/                 # CORS lab
├── insecuredes/         # Deserialization lab
├── jsonp/               # JSONP lab
├── postmessage/         # PostMessage lab
└── style/               # Shared CSS styles
```

## 🛠️ Troubleshooting

### Common Issues

**Database Connection Failed**
```bash
# Check MySQL service
sudo systemctl status mysql

# Reset root password if needed
sudo mysql_secure_installation
```

**File Upload Not Working**
```bash
# Check PHP configuration
php -i | grep upload

# Ensure directory permissions
chmod 755 upload/userfiles/
```

**XSS Labs Not Executing**
- Disable XSS protection in browser for testing
- Use developer tools to see console errors
- Check if JavaScript is enabled

**XXE Lab Issues**
- Ensure libxml is not configured to disable external entities
- Check if XML files are being processed correctly

## 🎓 Learning Path

### Beginner (Start Here)
1. **SQL Injection** - Learn database attack fundamentals
2. **XSS** - Understand client-side vulnerabilities
3. **CSRF** - Master state-changing attacks

### Intermediate
4. **IDOR** - Access control bypass techniques
5. **File Upload** - File handling vulnerabilities
6. **CORS** - Cross-origin security issues

### Advanced
7. **LFI** - File inclusion and log poisoning
8. **XXE** - XML processing attacks
9. **Deserialization** - Object injection techniques
10. **JSONP** - Callback manipulation
11. **PostMessage** - Cross-frame attacks

## 🔒 Security Notice

⚠️ **IMPORTANT**: These labs contain intentionally vulnerable code for educational purposes only.

- **Never deploy** these labs on production servers
- **Use only** in isolated testing environments
- **Understand** that these vulnerabilities are dangerous in real applications
- **Practice ethical hacking** - only test systems you own or have permission to test

## 📚 Additional Resources

- [OWASP Top 10](https://owasp.org/www-project-top-ten/)
- [OWASP Testing Guide](https://owasp.org/www-project-web-security-testing-guide/)
- [PortSwigger Web Security Academy](https://portswigger.net/web-security)
- [SANS Secure Coding Practices](https://www.sans.org/white-papers/2172/)

## 🤝 Contributing

Found an issue or want to improve a lab? Contributions are welcome!

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is for educational purposes only. Use responsibly and ethically.

---

**Happy Hacking! 🔓**  
*CyberWolf Security Training Platform*

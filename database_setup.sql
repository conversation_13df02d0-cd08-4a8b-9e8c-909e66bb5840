-- Database Setup for Web Penetration Testing Labs
-- CyberWolf Training Class - Compatible with XAMPP and hosting
-- Run this script to create all required tables and data

-- For XAMPP users: Make sure My<PERSON><PERSON> is running in XAMPP Control Panel
CREATE DATABASE IF NOT EXISTS test;
USE test;

-- Users table for SQL injection lab
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL,
    pass VARCHAR(100) NOT NULL
);

-- Insert dummy users
INSERT IGNORE INTO users (username, pass) VALUES 
('guest', 'guest'),
('admin', 'admin123'),
('user1', 'password1'),
('user2', 'password2');

-- Users table for CSRF lab
CREATE TABLE IF NOT EXISTS users_csrf (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL,
    email VARCHAR(100) NOT NULL,
    password VARCHAR(100) NOT NULL
);

-- Insert dummy CSRF users
INSERT IGNORE INTO users_csrf (username, email, password) VALUES 
('guest', '<EMAIL>', 'guest'),
('admin', '<EMAIL>', 'admin123');

-- Comments table for various labs
CREATE TABLE IF NOT EXISTS comments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uname VARCHAR(50) NOT NULL,
    comment TEXT NOT NULL,
    link VARCHAR(255) DEFAULT '#'
);

-- Insert dummy comments
INSERT IGNORE INTO comments (uname, comment, link) VALUES 
('admin', 'Welcome to our security testing platform!', '#'),
('guest', 'This is a test comment for demonstration.', '#'),
('user1', 'Great learning experience with these labs.', '#');

-- Books table for search functionality
CREATE TABLE IF NOT EXISTS books (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    auther VARCHAR(100) NOT NULL,
    story TEXT
);

-- Insert dummy books
INSERT IGNORE INTO books (name, auther, story) VALUES 
('Web Security Fundamentals', 'John Smith', 'A comprehensive guide to web application security testing and vulnerability assessment.'),
('Ethical Hacking Handbook', 'Jane Doe', 'Learn ethical hacking techniques and methodologies for penetration testing.'),
('SQL Injection Mastery', 'Bob Johnson', 'Deep dive into SQL injection attacks and prevention techniques.'),
('XSS Attack Vectors', 'Alice Brown', 'Understanding Cross-Site Scripting vulnerabilities and exploitation methods.'),
('CSRF Protection Guide', 'Mike Wilson', 'Complete guide to Cross-Site Request Forgery attacks and defenses.');

-- Contact table for XSS lab
CREATE TABLE IF NOT EXISTS contact (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    message TEXT NOT NULL,
    user_agent TEXT
);

-- Insert dummy contact entries
INSERT IGNORE INTO contact (name, email, message, user_agent) VALUES 
('Test User', '<EMAIL>', 'This is a test message for the contact form.', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'),
('Security Tester', '<EMAIL>', 'Testing the contact form functionality.', 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36');

-- Show created tables
SHOW TABLES;

-- Display table structures
DESCRIBE users;
DESCRIBE users_csrf;
DESCRIBE comments;
DESCRIBE books;
DESCRIBE contact;

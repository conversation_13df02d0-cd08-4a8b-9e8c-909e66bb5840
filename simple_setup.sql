-- Simple Database Setup for XAMPP
-- CyberWolf Training Class
-- Run this in smaller chunks if you get timeout errors

-- Create database
CREATE DATABASE IF NOT EXISTS test;
USE test;

-- Users table for SQL injection lab
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL,
    pass VARCHAR(100) NOT NULL
);

-- Insert users data
INSERT IGNORE INTO users (username, pass) VALUES 
('guest', 'guest'),
('admin', 'admin123'),
('user1', 'password1');

-- Users table for CSRF lab
CREATE TABLE IF NOT EXISTS users_csrf (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL,
    email VARCHAR(100) NOT NULL,
    password VARCHAR(100) NOT NULL
);

-- Insert CSRF users data
INSERT IGNORE INTO users_csrf (username, email, password) VALUES 
('guest', '<EMAIL>', 'guest'),
('admin', '<EMAIL>', 'admin123');

-- Comments table for XSS lab
CREATE TABLE IF NOT EXISTS comments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uname VARCHAR(50) NOT NULL,
    comment TEXT NOT NULL,
    link VARCHAR(255) DEFAULT '#'
);

-- Insert comments data
INSERT IGNORE INTO comments (uname, comment, link) VALUES 
('admin', 'Welcome to CyberWolf Security Labs!', '#'),
('guest', 'This is a test comment for demonstration.', '#');

-- Books table for IDOR lab
CREATE TABLE IF NOT EXISTS books (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    auther VARCHAR(100) NOT NULL,
    story TEXT
);

-- Insert books data
INSERT IGNORE INTO books (name, auther, story) VALUES 
('Web Security Fundamentals', 'CyberWolf Team', 'Complete guide to web security testing.'),
('Ethical Hacking Handbook', 'Security Expert', 'Learn ethical hacking techniques.');

-- Contact table for various labs
CREATE TABLE IF NOT EXISTS contact (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    message TEXT NOT NULL,
    user_agent TEXT
);

-- Insert contact data
INSERT IGNORE INTO contact (name, email, message, user_agent) VALUES 
('Test User', '<EMAIL>', 'Test message for contact form.', 'Mozilla/5.0');

-- Show tables to verify
SHOW TABLES;

<?php
/**
 * Database Setup Script for CyberWolf Labs
 * This script creates all necessary tables for the security labs
 */

// Include database configuration
include_once 'db_config.php';

echo "<h2>🗄️ Database Setup for CyberWolf Labs</h2>";
echo "<pre>";

// Test database connection
echo "Testing database connection...\n";
$test_result = testDatabaseConnection();

if ($test_result['success']) {
    echo "✅ Database connection successful!\n\n";
    
    echo "📊 Current status:\n";
    echo "Existing tables: " . implode(', ', $test_result['existing_tables']) . "\n";
    
    if (!empty($test_result['missing_tables'])) {
        echo "Missing tables: " . implode(', ', $test_result['missing_tables']) . "\n\n";
        
        echo "🔧 Creating missing tables...\n";
        if (createDatabaseTables()) {
            echo "✅ All database tables created successfully!\n\n";
            
            // Test again to show final status
            $final_test = testDatabaseConnection();
            echo "📊 Final status:\n";
            echo "All tables: " . implode(', ', $final_test['existing_tables']) . "\n";
        } else {
            echo "❌ Failed to create some database tables\n";
        }
    } else {
        echo "✅ All required tables already exist!\n";
    }
} else {
    echo "❌ Database connection failed!\n";
    echo "Error: " . $test_result['error'] . "\n\n";
    
    echo "💡 Please check your database configuration:\n";
    echo "1. Make sure MySQL/MariaDB is running\n";
    echo "2. Verify database credentials in db_config.php\n";
    echo "3. Ensure the 'test' database exists\n";
}

echo "\n🌐 After setup, you can access:\n";
echo "- Main Labs: http://localhost/lab/\n";
echo "- SQL Lab: http://localhost/lab/sql/login.php\n";
echo "- Status Page: http://localhost/lab/status.php\n";

echo "</pre>";
?>

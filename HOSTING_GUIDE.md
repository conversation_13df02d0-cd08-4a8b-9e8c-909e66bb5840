# 🌐 CyberWolf Labs - Hosting Deployment Guide

## Domain: cyberwolf.wuaze.com

This guide will help you deploy the CyberWolf Web Penetration Testing Labs on your hosting environment.

## 📋 Pre-Deployment Checklist

### Hosting Requirements
- ✅ **PHP 7.0+** with extensions: mysqli, xml, curl
- ✅ **MySQL/MariaDB** database
- ✅ **Apache/Nginx** web server
- ✅ **SSL Certificate** (recommended for HTTPS)
- ✅ **File Upload** permissions
- ✅ **Directory Write** permissions

### Domain Configuration
- **Primary Domain**: `cyberwolf.wuaze.com`
- **Labs Path**: `/labs/`
- **Full URL**: `https://cyberwolf.wuaze.com/labs/`

## 🚀 Deployment Steps

### Step 1: Upload Files
Upload all lab files to your hosting directory:
```
public_html/labs/
├── index.php
├── answers.html
├── db_config.php
├── hosting_config.php
├── sql/
├── xss/
├── csrf/
├── idor/
├── upload/
├── file inclusion/
├── xxe/
├── cors/
├── insecuredes/
├── jsonp/
├── postmessage/
└── style/
```

### Step 2: Database Setup

#### 2.1 Create Database
In your hosting control panel (cPanel/Plesk):
1. Go to **MySQL Databases**
2. Create database: `cyberwolf_labs`
3. Create user: `cyberwolf_user`
4. Set strong password
5. Grant all privileges to user

#### 2.2 Update Database Configuration
Edit `db_config.php`:
```php
$db_config = [
    'host' => 'localhost',  // Usually localhost
    'username' => 'your_cpanel_username_cyberwolf_user',
    'password' => 'your_secure_database_password',
    'database' => 'your_cpanel_username_cyberwolf_labs',
    'charset' => 'utf8mb4'
];
```

#### 2.3 Initialize Database
Visit: `https://cyberwolf.wuaze.com/labs/db_config.php`
This will automatically create all required tables and data.

### Step 3: Configure Hosting Environment
Visit: `https://cyberwolf.wuaze.com/labs/hosting_config.php`
This will:
- Validate domain configuration
- Check PHP requirements
- Create .htaccess file
- Set proper permissions

### Step 4: Set Directory Permissions
Ensure these directories are writable (755 or 775):
```bash
chmod 755 labs/upload/userfiles/
chmod 755 labs/xxe/files/
chmod 755 labs/file\ inclusion/files/
```

### Step 5: SSL Configuration
Ensure SSL is properly configured:
- Force HTTPS redirects
- Valid SSL certificate
- Secure headers enabled

## 🔧 Hosting-Specific Configurations

### Shared Hosting (cPanel)
```php
// In db_config.php
$db_config = [
    'host' => 'localhost',
    'username' => 'cpanel_user_cyberwolf',
    'password' => 'your_password',
    'database' => 'cpanel_user_cyberwolf_labs'
];
```

### VPS/Dedicated Server
```php
// In db_config.php
$db_config = [
    'host' => 'localhost',
    'username' => 'cyberwolf_user',
    'password' => 'secure_password',
    'database' => 'cyberwolf_labs'
];
```

### Cloud Hosting (AWS/DigitalOcean)
```php
// In db_config.php
$db_config = [
    'host' => 'your-rds-endpoint.amazonaws.com',
    'username' => 'cyberwolf_user',
    'password' => 'secure_password',
    'database' => 'cyberwolf_labs'
];
```

## 🛡️ Security Considerations

### Production Security
⚠️ **IMPORTANT**: These labs contain intentionally vulnerable code!

1. **Isolate Environment**: Use separate subdomain/server
2. **Access Control**: Implement IP restrictions if needed
3. **Monitoring**: Enable logging and monitoring
4. **Backups**: Regular database and file backups
5. **Updates**: Keep PHP and MySQL updated

### .htaccess Security
The hosting configuration creates a secure .htaccess:
```apache
# Force HTTPS
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Protect sensitive files
<Files "db_config.php">
    Order Allow,Deny
    Deny from all
</Files>

# PHP Security Settings
php_value upload_max_filesize 10M
php_value post_max_size 10M
php_value max_execution_time 30
```

## 🧪 Testing Deployment

### 1. Basic Functionality Test
Visit each URL and verify it loads:
- `https://cyberwolf.wuaze.com/labs/` - Main dashboard
- `https://cyberwolf.wuaze.com/labs/answers.html` - Solutions
- `https://cyberwolf.wuaze.com/labs/sql/` - SQL injection lab
- `https://cyberwolf.wuaze.com/labs/xss/` - XSS lab

### 2. Database Connectivity Test
- Try logging into any lab
- Check if data is being stored/retrieved
- Verify all tables exist

### 3. File Upload Test
- Go to upload lab
- Try uploading a test file
- Check if files are stored properly

### 4. Security Headers Test
Use online tools to verify:
- SSL certificate validity
- Security headers presence
- HTTPS enforcement

## 🔍 Troubleshooting

### Common Issues

**Database Connection Failed**
```
Error: Access denied for user 'cyberwolf_user'@'localhost'
Solution: Check database credentials in db_config.php
```

**File Upload Not Working**
```
Error: Permission denied
Solution: chmod 755 upload/userfiles/
```

**XSS Labs Not Working**
```
Error: Content Security Policy blocking
Solution: Disable CSP for lab functionality
```

**SSL Certificate Issues**
```
Error: Mixed content warnings
Solution: Ensure all resources use HTTPS
```

### Debug Mode
Enable debug mode by adding to any lab file:
```php
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

## 📊 Monitoring & Analytics

### Log Files to Monitor
- Apache/Nginx access logs
- PHP error logs
- MySQL slow query logs
- Application-specific logs

### Performance Monitoring
- Page load times
- Database query performance
- Server resource usage
- SSL certificate expiry

## 🔄 Maintenance

### Regular Tasks
1. **Database Cleanup**: Remove test data periodically
2. **Log Rotation**: Manage log file sizes
3. **Security Updates**: Keep software updated
4. **Backup Verification**: Test backup restoration
5. **Performance Optimization**: Monitor and optimize

### Update Procedure
1. Backup current installation
2. Test updates in staging environment
3. Deploy during low-traffic periods
4. Verify functionality post-update
5. Monitor for issues

## 📞 Support

### Resources
- **Documentation**: Check README.md for detailed info
- **Configuration**: Use hosting_config.php for setup
- **Database**: Use db_config.php for DB issues
- **Answers**: Check answers.html for solutions

### Contact Information
- **Domain**: cyberwolf.wuaze.com
- **Platform**: CyberWolf Security Training
- **Purpose**: Educational web security testing

---

**🎯 Deployment Complete!**

Your CyberWolf Labs should now be fully functional at:
**https://cyberwolf.wuaze.com/labs/**

Remember to test all functionality and implement appropriate security measures for your hosting environment.

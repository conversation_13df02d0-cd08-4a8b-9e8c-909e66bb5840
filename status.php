<?php
/**
 * CyberWolf Labs - System Status Check
 * Domain: cyberwolf.wuaze.com
 */

// Include configurations
if (file_exists('db_config.php')) {
    include_once 'db_config.php';
}
if (file_exists('hosting_config.php')) {
    include_once 'hosting_config.php';
}

// Set content type
header('Content-Type: text/html; charset=UTF-8');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Status - CyberWolf Labs</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #0a0a0a;
            color: #00ff00;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #00ff00;
            background: rgba(0, 255, 0, 0.05);
        }
        .status-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #333;
            background: rgba(0, 255, 0, 0.02);
        }
        .status-item {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 5px;
        }
        .status-ok { color: #00ff00; }
        .status-warning { color: #ffaa00; }
        .status-error { color: #ff0000; }
        .info-box {
            background: #111;
            border: 1px solid #333;
            padding: 10px;
            margin: 10px 0;
            font-size: 0.9em;
        }
        .refresh-btn {
            background: rgba(0, 255, 0, 0.1);
            border: 1px solid #00ff00;
            color: #00ff00;
            padding: 10px 20px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px 0;
        }
        .refresh-btn:hover {
            background: rgba(0, 255, 0, 0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 CYBERWOLF LABS - SYSTEM STATUS</h1>
            <p>Domain: cyberwolf.wuaze.com</p>
            <p>Status Check: <?php echo date('Y-m-d H:i:s'); ?></p>
        </div>

        <!-- Domain Status -->
        <div class="status-section">
            <h2>🌐 Domain & Hosting Status</h2>
            <?php
            $current_domain = $_SERVER['HTTP_HOST'] ?? 'unknown';
            $is_https = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on';
            $expected_domain = 'cyberwolf.wuaze.com';
            ?>
            <div class="status-item">
                <span>Current Domain:</span>
                <span class="<?php echo ($current_domain === $expected_domain) ? 'status-ok' : 'status-warning'; ?>">
                    <?php echo $current_domain; ?>
                </span>
            </div>
            <div class="status-item">
                <span>HTTPS Status:</span>
                <span class="<?php echo $is_https ? 'status-ok' : 'status-warning'; ?>">
                    <?php echo $is_https ? 'Enabled' : 'Disabled'; ?>
                </span>
            </div>
            <div class="status-item">
                <span>Server Software:</span>
                <span class="status-ok"><?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></span>
            </div>
        </div>

        <!-- PHP Status -->
        <div class="status-section">
            <h2>🐘 PHP Configuration</h2>
            <div class="status-item">
                <span>PHP Version:</span>
                <span class="<?php echo version_compare(PHP_VERSION, '7.0.0', '>=') ? 'status-ok' : 'status-error'; ?>">
                    <?php echo PHP_VERSION; ?>
                </span>
            </div>
            <div class="status-item">
                <span>MySQLi Extension:</span>
                <span class="<?php echo extension_loaded('mysqli') ? 'status-ok' : 'status-error'; ?>">
                    <?php echo extension_loaded('mysqli') ? 'Loaded' : 'Missing'; ?>
                </span>
            </div>
            <div class="status-item">
                <span>XML Extension:</span>
                <span class="<?php echo extension_loaded('xml') ? 'status-ok' : 'status-error'; ?>">
                    <?php echo extension_loaded('xml') ? 'Loaded' : 'Missing'; ?>
                </span>
            </div>
            <div class="status-item">
                <span>cURL Extension:</span>
                <span class="<?php echo extension_loaded('curl') ? 'status-ok' : 'status-warning'; ?>">
                    <?php echo extension_loaded('curl') ? 'Loaded' : 'Missing'; ?>
                </span>
            </div>
            <div class="status-item">
                <span>File Uploads:</span>
                <span class="<?php echo ini_get('file_uploads') ? 'status-ok' : 'status-error'; ?>">
                    <?php echo ini_get('file_uploads') ? 'Enabled' : 'Disabled'; ?>
                </span>
            </div>
            <div class="status-item">
                <span>Max Upload Size:</span>
                <span class="status-ok"><?php echo ini_get('upload_max_filesize'); ?></span>
            </div>
        </div>

        <!-- Database Status -->
        <div class="status-section">
            <h2>🗄️ Database Status</h2>
            <?php
            $db_status = 'Unknown';
            $db_class = 'status-warning';
            $db_tables = [];
            
            if (function_exists('testDatabaseConnection')) {
                $test_result = testDatabaseConnection();
                if ($test_result['success']) {
                    $db_status = 'Connected';
                    $db_class = 'status-ok';
                    $db_tables = $test_result['existing_tables'];
                } else {
                    $db_status = 'Connection Failed';
                    $db_class = 'status-error';
                }
            } else {
                // Fallback test
                $conn = @mysqli_connect('localhost', 'root', '', 'test');
                if ($conn) {
                    $db_status = 'Connected (Fallback)';
                    $db_class = 'status-ok';
                    mysqli_close($conn);
                } else {
                    $db_status = 'Connection Failed';
                    $db_class = 'status-error';
                }
            }
            ?>
            <div class="status-item">
                <span>Database Connection:</span>
                <span class="<?php echo $db_class; ?>"><?php echo $db_status; ?></span>
            </div>
            <?php if (!empty($db_tables)): ?>
            <div class="status-item">
                <span>Tables Found:</span>
                <span class="status-ok"><?php echo implode(', ', $db_tables); ?></span>
            </div>
            <?php endif; ?>
        </div>

        <!-- File System Status -->
        <div class="status-section">
            <h2>📁 File System Status</h2>
            <?php
            $directories = [
                'upload/userfiles' => 'Upload Directory',
                'xxe/files' => 'XXE Files Directory',
                'file inclusion/files' => 'LFI Files Directory'
            ];
            
            foreach ($directories as $dir => $name):
                $exists = is_dir($dir);
                $writable = $exists && is_writable($dir);
            ?>
            <div class="status-item">
                <span><?php echo $name; ?>:</span>
                <span class="<?php echo $writable ? 'status-ok' : ($exists ? 'status-warning' : 'status-error'); ?>">
                    <?php 
                    if ($writable) echo 'Writable';
                    elseif ($exists) echo 'Exists (Not Writable)';
                    else echo 'Missing';
                    ?>
                </span>
            </div>
            <?php endforeach; ?>
        </div>

        <!-- Lab Status -->
        <div class="status-section">
            <h2>🧪 Lab Status</h2>
            <?php
            $labs = [
                'sql' => 'SQL Injection Lab',
                'xss' => 'XSS Lab',
                'csrf' => 'CSRF Lab',
                'idor' => 'IDOR Lab',
                'upload' => 'File Upload Lab',
                'file inclusion' => 'LFI Lab',
                'xxe' => 'XXE Lab',
                'cors' => 'CORS Lab',
                'insecuredes' => 'Deserialization Lab',
                'jsonp' => 'JSONP Lab',
                'postmessage' => 'PostMessage Lab'
            ];
            
            foreach ($labs as $dir => $name):
                $exists = is_dir($dir);
                $has_index = file_exists($dir . '/index.php') || file_exists($dir . '/login.php');
            ?>
            <div class="status-item">
                <span><?php echo $name; ?>:</span>
                <span class="<?php echo ($exists && $has_index) ? 'status-ok' : 'status-warning'; ?>">
                    <?php echo ($exists && $has_index) ? 'Available' : 'Issues Detected'; ?>
                </span>
            </div>
            <?php endforeach; ?>
        </div>

        <!-- Quick Actions -->
        <div class="status-section">
            <h2>⚡ Quick Actions</h2>
            <a href="status.php" class="refresh-btn">🔄 Refresh Status</a>
            <a href="index.php" class="refresh-btn">🏠 Go to Labs</a>
            <a href="answers.html" class="refresh-btn">📖 View Answers</a>
            <a href="db_config.php" class="refresh-btn">🗄️ Database Setup</a>
            <a href="hosting_config.php" class="refresh-btn">⚙️ Hosting Config</a>
        </div>

        <!-- System Information -->
        <div class="info-box">
            <strong>System Information:</strong><br>
            Server Time: <?php echo date('Y-m-d H:i:s T'); ?><br>
            PHP Memory Limit: <?php echo ini_get('memory_limit'); ?><br>
            Max Execution Time: <?php echo ini_get('max_execution_time'); ?>s<br>
            Document Root: <?php echo $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown'; ?><br>
            Current Directory: <?php echo __DIR__; ?>
        </div>

        <div class="info-box">
            <strong>🔗 Access URLs:</strong><br>
            Main Dashboard: <a href="https://cyberwolf.wuaze.com/labs/" style="color: #00ffff;">https://cyberwolf.wuaze.com/labs/</a><br>
            Lab Answers: <a href="https://cyberwolf.wuaze.com/labs/answers.html" style="color: #00ffff;">https://cyberwolf.wuaze.com/labs/answers.html</a><br>
            System Status: <a href="https://cyberwolf.wuaze.com/labs/status.php" style="color: #00ffff;">https://cyberwolf.wuaze.com/labs/status.php</a>
        </div>
    </div>
</body>
</html>

<?php

# Write valid syntax - Context-based XSS

$input = @$_GET['q'];

function check($val){
	// Simple filter that can be bypassed
	$blocked = array('script', 'alert', 'javascript');
	$val = str_ireplace($blocked, '', $val);
	return $val;
}

?>

<html>
<head>
    <title>XSS Filter Bypass - Context Based</title>
</head>
<body>
    <h2>XSS Filter Bypass Challenge 3</h2>
    <p>Try to execute JavaScript in this context:</p>

    <div id="xssMe"></div>

    <script type="text/javascript">
        function vulnFunc(){
            var x = "<?php echo check($input);?>";
            document.getElementById('xssMe').innerHTML = x;
        }

        // Auto-execute the function
        vulnFunc();
    </script>

    <p><strong>Hint:</strong> The input is placed inside a JavaScript string. Try breaking out of the string context.</p>
    <p><strong>Example:</strong> ?q="; alert('XSS'); //</p>
</body>
</html>
<?php
/**
 * XAMPP Setup Script for CyberWolf Labs
 * This script helps set up the labs for local XAMPP environment
 */

header('Content-Type: text/html; charset=UTF-8');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XAMPP Setup - CyberWolf Labs</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #0a0a0a;
            color: #00ff00;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #00ff00;
            background: rgba(0, 255, 0, 0.05);
        }
        .step {
            background: #111;
            border: 1px solid #333;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }
        .success { color: #00ff00; }
        .error { color: #ff0000; }
        .warning { color: #ffaa00; }
        .info { color: #00ffff; }
        .code {
            background: #222;
            border: 1px solid #444;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            border-radius: 3px;
        }
        .button {
            background: rgba(0, 255, 0, 0.1);
            border: 1px solid #00ff00;
            color: #00ff00;
            padding: 10px 20px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px 5px;
            border-radius: 3px;
        }
        .button:hover {
            background: rgba(0, 255, 0, 0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🐺 CyberWolf Labs - XAMPP Setup</h1>
            <p>Local Development Environment Configuration</p>
        </div>

        <div class="step">
            <h2>📋 Pre-Setup Checklist</h2>
            <p>Before proceeding, ensure you have:</p>
            <ul>
                <li>✅ XAMPP installed and running</li>
                <li>✅ Apache and MySQL services started</li>
                <li>✅ Labs files in htdocs directory</li>
            </ul>
        </div>

        <div class="step">
            <h2>🔧 Automatic Database Setup</h2>
            <?php
            // Test database connection
            $conn = @mysqli_connect('localhost', 'root', '', 'test');
            
            if (!$conn) {
                echo '<p class="error">❌ Database connection failed: ' . mysqli_connect_error() . '</p>';
                echo '<p class="warning">⚠️ Please ensure MySQL is running in XAMPP Control Panel</p>';
            } else {
                echo '<p class="success">✅ Database connection successful</p>';
                
                // Check if tables exist
                $tables = ['users', 'users_csrf', 'comments', 'books', 'contact'];
                $existing_tables = [];
                
                foreach ($tables as $table) {
                    $result = mysqli_query($conn, "SHOW TABLES LIKE '$table'");
                    if (mysqli_num_rows($result) > 0) {
                        $existing_tables[] = $table;
                    }
                }
                
                if (count($existing_tables) == count($tables)) {
                    echo '<p class="success">✅ All required tables exist</p>';
                } else {
                    echo '<p class="warning">⚠️ Some tables are missing</p>';
                    echo '<p class="info">Missing tables: ' . implode(', ', array_diff($tables, $existing_tables)) . '</p>';
                }
                
                mysqli_close($conn);
            }
            ?>
            
            <div class="code">
                <strong>Manual Setup (if needed):</strong><br>
                1. Open phpMyAdmin: http://localhost/phpmyadmin<br>
                2. Import database_setup.sql file<br>
                3. Or run: mysql -u root -p &lt; database_setup.sql
            </div>
        </div>

        <div class="step">
            <h2>🌐 Environment Detection</h2>
            <?php
            $domain = $_SERVER['HTTP_HOST'] ?? 'localhost';
            $is_local = (strpos($domain, 'localhost') !== false || strpos($domain, '127.0.0.1') !== false);
            
            if ($is_local) {
                echo '<p class="success">✅ Local XAMPP environment detected</p>';
                echo '<p class="info">Domain: ' . $domain . '</p>';
                echo '<p class="info">Database config will use: root user with empty password</p>';
            } else {
                echo '<p class="warning">⚠️ Non-local environment detected</p>';
                echo '<p class="info">Domain: ' . $domain . '</p>';
                echo '<p class="info">You may need to update database credentials</p>';
            }
            ?>
        </div>

        <div class="step">
            <h2>📁 Directory Structure Check</h2>
            <?php
            $required_dirs = [
                'sql' => 'SQL Injection Lab',
                'xss' => 'XSS Lab',
                'csrf' => 'CSRF Lab',
                'idor' => 'IDOR Lab',
                'upload' => 'File Upload Lab',
                'file inclusion' => 'File Inclusion Lab',
                'xxe' => 'XXE Lab',
                'cors' => 'CORS Lab',
                'insecuredes' => 'Deserialization Lab',
                'jsonp' => 'JSONP Lab',
                'postmessage' => 'PostMessage Lab'
            ];
            
            $missing_dirs = [];
            foreach ($required_dirs as $dir => $name) {
                if (is_dir($dir)) {
                    echo '<p class="success">✅ ' . $name . '</p>';
                } else {
                    echo '<p class="error">❌ ' . $name . ' (missing)</p>';
                    $missing_dirs[] = $dir;
                }
            }
            
            if (empty($missing_dirs)) {
                echo '<p class="success">✅ All lab directories found</p>';
            } else {
                echo '<p class="warning">⚠️ Missing directories: ' . implode(', ', $missing_dirs) . '</p>';
            }
            ?>
        </div>

        <div class="step">
            <h2>🧪 Quick Lab Test</h2>
            <p>Test your lab setup:</p>
            <a href="index.php" class="button">🏠 Main Dashboard</a>
            <a href="sql/" class="button">🗄️ SQL Lab</a>
            <a href="xss/" class="button">🔍 XSS Lab</a>
            <a href="answers.html" class="button">📖 Solutions</a>
            <a href="status.php" class="button">📊 System Status</a>
        </div>

        <div class="step">
            <h2>🔧 Troubleshooting</h2>
            <div class="code">
                <strong>Common Issues:</strong><br><br>
                
                <strong>1. Database Connection Failed:</strong><br>
                - Start MySQL in XAMPP Control Panel<br>
                - Check if port 3306 is available<br>
                - Verify MySQL service is running<br><br>
                
                <strong>2. Apache Not Starting:</strong><br>
                - Check if port 80/443 is available<br>
                - Stop IIS or other web servers<br>
                - Run XAMPP as administrator<br><br>
                
                <strong>3. Labs Not Loading:</strong><br>
                - Ensure files are in htdocs/lab/ directory<br>
                - Check file permissions<br>
                - Verify PHP is enabled<br><br>
                
                <strong>4. File Upload Issues:</strong><br>
                - Create upload/userfiles/ directory<br>
                - Set proper write permissions<br>
                - Check PHP upload settings
            </div>
        </div>

        <div class="step">
            <h2>📚 Next Steps</h2>
            <ol>
                <li>✅ Verify all services are running</li>
                <li>✅ Test database connectivity</li>
                <li>✅ Access main lab dashboard</li>
                <li>✅ Try a simple lab (SQL injection)</li>
                <li>✅ Check answers.html for solutions</li>
            </ol>
            
            <p class="info">🎯 Your labs should be accessible at: <strong>http://localhost/lab/</strong></p>
        </div>

        <div class="header">
            <p>🐺 CyberWolf Training Class</p>
            <p>Happy Learning! 🚀</p>
        </div>
    </div>
</body>
</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web Penetration Testing Labs - CyberWolf</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px 0;
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 2em;
            margin-bottom: 10px;
            color: #333;
            font-weight: bold;
        }

        .trophy-icon {
            font-size: 1.5em;
            margin: 0 10px;
        }

        .leaderboard-title {
            background: #fff;
            padding: 15px;
            text-align: center;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            border: 2px solid #ddd;
        }

        .leaderboard-title h2 {
            color: #333;
            font-size: 1.3em;
            font-weight: bold;
        }

        .lab-item {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            border: 2px solid #ddd;
            transition: all 0.3s ease;
        }

        .lab-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.15);
        }

        .lab-item.champion {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border-color: #f39c12;
        }

        .lab-item.runner-up {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border-color: #2196f3;
        }

        .lab-item.third-place {
            background: linear-gradient(135deg, #fce4ec, #f8bbd9);
            border-color: #e91e63;
        }

        .lab-left {
            display: flex;
            align-items: center;
            flex: 1;
        }

        .rank-badge {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
            font-size: 1.2em;
        }

        .rank-1 { background: #f39c12; color: white; }
        .rank-2 { background: #2196f3; color: white; }
        .rank-3 { background: #e91e63; color: white; }
        .rank-other { background: #95a5a6; color: white; }

        .lab-info {
            flex: 1;
        }

        .lab-name {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 5px;
            color: #333;
        }

        .lab-description {
            color: #666;
            font-size: 0.9em;
        }

        .lab-badge {
            background: #f39c12;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: bold;
            margin-left: 10px;
        }

        .runner-up-badge {
            background: #2196f3;
        }

        .third-place-badge {
            background: #e91e63;
        }

        .lab-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .points {
            text-align: center;
        }

        .points-number {
            font-size: 1.8em;
            font-weight: bold;
            color: #333;
        }

        .points-label {
            font-size: 0.8em;
            color: #666;
            text-transform: uppercase;
        }

        .progress-bar {
            width: 100px;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: #f39c12;
            border-radius: 4px;
        }

        .top-badge {
            background: #333;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><span class="trophy-icon">🏆</span>Web Penetration Testing Labs<span class="trophy-icon">🏆</span></h1>
            <p>CyberWolf Security Training Platform</p>
            <p style="font-size: 1.1em; color: #00ff00;">🐺 The labs developed for CyberWolf Training Class 🐺</p>
            <p style="font-size: 0.9em; opacity: 0.7;">Hosted at: cyberwolf.wuaze.com</p>
        </div>

        <div class="leaderboard-title">
            <h2><span class="trophy-icon">🏆</span> ALL PARTICIPANTS LEADERBOARD <span class="trophy-icon">🏆</span></h2>
        </div>

        <!-- Champion -->
        <div class="lab-item champion">
            <div class="lab-left">
                <div class="rank-badge rank-1">#1</div>
                <div class="lab-info">
                    <div class="lab-name">
                        SQL Injection
                        <span class="lab-badge">CHAMPION</span>
                    </div>
                    <div class="lab-description">5/5 problems solved</div>
                </div>
            </div>
            <div class="lab-right">
                <div class="points">
                    <div class="points-number">200</div>
                    <div class="points-label">POINTS</div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 100%;"></div>
                </div>
                <a href="sql/" class="top-badge">TOP 1</a>
            </div>
        </div>

        <!-- Runner-up -->
        <div class="lab-item runner-up">
            <div class="lab-left">
                <div class="rank-badge rank-2">#2</div>
                <div class="lab-info">
                    <div class="lab-name">
                        Cross-Site Scripting (XSS)
                        <span class="lab-badge runner-up-badge">RUNNER-UP</span>
                    </div>
                    <div class="lab-description">4/5 problems solved</div>
                </div>
            </div>
            <div class="lab-right">
                <div class="points">
                    <div class="points-number">180</div>
                    <div class="points-label">POINTS</div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 80%; background: #2196f3;"></div>
                </div>
                <a href="xss/" class="top-badge">TOP 2</a>
            </div>
        </div>

        <!-- Third Place -->
        <div class="lab-item third-place">
            <div class="lab-left">
                <div class="rank-badge rank-3">#3</div>
                <div class="lab-info">
                    <div class="lab-name">
                        Cross-Site Request Forgery
                        <span class="lab-badge third-place-badge">THIRD PLACE</span>
                    </div>
                    <div class="lab-description">3/5 problems solved</div>
                </div>
            </div>
            <div class="lab-right">
                <div class="points">
                    <div class="points-number">150</div>
                    <div class="points-label">POINTS</div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 60%; background: #e91e63;"></div>
                </div>
                <a href="csrf/" class="top-badge">TOP 3</a>
            </div>
        </div>

        <!-- Other Labs -->
        <div class="lab-item">
            <div class="lab-left">
                <div class="rank-badge rank-other">#4</div>
                <div class="lab-info">
                    <div class="lab-name">Insecure Direct Object Reference</div>
                    <div class="lab-description">2/5 problems solved</div>
                </div>
            </div>
            <div class="lab-right">
                <div class="points">
                    <div class="points-number">120</div>
                    <div class="points-label">POINTS</div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 40%; background: #95a5a6;"></div>
                </div>
                <a href="idor/" class="top-badge">START</a>
            </div>
        </div>

        <div class="lab-item">
            <div class="lab-left">
                <div class="rank-badge rank-other">#5</div>
                <div class="lab-info">
                    <div class="lab-name">File Upload Vulnerabilities</div>
                    <div class="lab-description">2/5 problems solved</div>
                </div>
            </div>
            <div class="lab-right">
                <div class="points">
                    <div class="points-number">100</div>
                    <div class="points-label">POINTS</div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 30%; background: #95a5a6;"></div>
                </div>
                <a href="upload/" class="top-badge">START</a>
            </div>
        </div>

        <div class="lab-item">
            <div class="lab-left">
                <div class="rank-badge rank-other">#6</div>
                <div class="lab-info">
                    <div class="lab-name">Local File Inclusion</div>
                    <div class="lab-description">1/5 problems solved</div>
                </div>
            </div>
            <div class="lab-right">
                <div class="points">
                    <div class="points-number">80</div>
                    <div class="points-label">POINTS</div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 20%; background: #95a5a6;"></div>
                </div>
                <a href="file inclusion/" class="top-badge">START</a>
            </div>
        </div>

        <div class="lab-item">
            <div class="lab-left">
                <div class="rank-badge rank-other">#7</div>
                <div class="lab-info">
                    <div class="lab-name">XML External Entity (XXE)</div>
                    <div class="lab-description">1/5 problems solved</div>
                </div>
            </div>
            <div class="lab-right">
                <div class="points">
                    <div class="points-number">60</div>
                    <div class="points-label">POINTS</div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 15%; background: #95a5a6;"></div>
                </div>
                <a href="xxe/" class="top-badge">START</a>
            </div>
        </div>

        <div class="lab-item">
            <div class="lab-left">
                <div class="rank-badge rank-other">#8</div>
                <div class="lab-info">
                    <div class="lab-name">CORS Misconfiguration</div>
                    <div class="lab-description">0/5 problems solved</div>
                </div>
            </div>
            <div class="lab-right">
                <div class="points">
                    <div class="points-number">40</div>
                    <div class="points-label">POINTS</div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 10%; background: #95a5a6;"></div>
                </div>
                <a href="cors/" class="top-badge">START</a>
            </div>
        </div>

        <div class="lab-item">
            <div class="lab-left">
                <div class="rank-badge rank-other">#9</div>
                <div class="lab-info">
                    <div class="lab-name">Insecure Deserialization</div>
                    <div class="lab-description">0/5 problems solved</div>
                </div>
            </div>
            <div class="lab-right">
                <div class="points">
                    <div class="points-number">20</div>
                    <div class="points-label">POINTS</div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 5%; background: #95a5a6;"></div>
                </div>
                <a href="insecuredes/" class="top-badge">START</a>
            </div>
        </div>

        <div class="lab-item">
            <div class="lab-left">
                <div class="rank-badge rank-other">#10</div>
                <div class="lab-info">
                    <div class="lab-name">JSONP Vulnerabilities</div>
                    <div class="lab-description">0/5 problems solved</div>
                </div>
            </div>
            <div class="lab-right">
                <div class="points">
                    <div class="points-number">10</div>
                    <div class="points-label">POINTS</div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 2%; background: #95a5a6;"></div>
                </div>
                <a href="jsonp/" class="top-badge">START</a>
            </div>
        </div>

        <div class="lab-item">
            <div class="lab-left">
                <div class="rank-badge rank-other">#11</div>
                <div class="lab-info">
                    <div class="lab-name">PostMessage Vulnerabilities</div>
                    <div class="lab-description">0/5 problems solved</div>
                </div>
            </div>
            <div class="lab-right">
                <div class="points">
                    <div class="points-number">5</div>
                    <div class="points-label">POINTS</div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 1%; background: #95a5a6;"></div>
                </div>
                <a href="postmessage/" class="top-badge">START</a>
            </div>
        </div>
    </div>

    <script>
        // Add interactive effects for leaderboard
        document.addEventListener('DOMContentLoaded', function() {
            const labItems = document.querySelectorAll('.lab-item');

            labItems.forEach(item => {
                item.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-3px) scale(1.01)';
                });

                item.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Add click animation to buttons
            const buttons = document.querySelectorAll('.top-badge');
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            });

            // Animate progress bars on load
            const progressBars = document.querySelectorAll('.progress-fill');
            progressBars.forEach((bar, index) => {
                const width = bar.style.width;
                bar.style.width = '0%';
                setTimeout(() => {
                    bar.style.transition = 'width 1s ease-out';
                    bar.style.width = width;
                }, index * 200);
            });
        });
    </script>
</body>
</html>

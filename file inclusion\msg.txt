🐺 Welcome to CyberWolf Training Class - File Inclusion Lab 🐺

This lab is developed for CyberWolf Training Class to demonstrate Local File Inclusion (LFI) vulnerabilities.

📚 Learning Objectives:
- Understand file inclusion vulnerabilities
- Learn directory traversal techniques
- Practice log poisoning attacks
- Master path validation bypasses

🎯 Lab Instructions:
1. Try to include different files using the 'page' parameter
2. Attempt directory traversal attacks (../../../etc/passwd)
3. Test log poisoning techniques
4. Explore different bypass methods

⚠️ Security Notice:
This lab contains intentionally vulnerable code for educational purposes only.
Use only in authorized testing environments.

🔗 CyberWolf Training Platform: cyberwolf.wuaze.com
📖 Complete solutions available in answers.html

Happy Learning! 🚀
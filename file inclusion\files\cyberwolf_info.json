{"platform_info": {"name": "CyberWolf Training Class", "domain": "cyberwolf.wuaze.com", "description": "Professional cybersecurity training platform specializing in hands-on web application security", "mission": "Empowering security professionals with practical, real-world cybersecurity skills"}, "training_philosophy": {"approach": "Hands-on Learning", "methodology": "Learn by doing with intentionally vulnerable applications", "focus": "Practical skills that translate directly to real-world scenarios"}, "course_offerings": {"web_security": {"title": "Web Application Security Testing", "modules": ["SQL Injection Attacks", "Cross-Site Scripting (XSS)", "Cross-Site Request Forgery (CSRF)", "Insecure Direct Object References (IDOR)", "File Upload Vulnerabilities", "Local/Remote File Inclusion", "XML External Entity (XXE) Attacks", "CORS Misconfigurations", "Insecure Deserialization", "JSONP Vulnerabilities", "PostMessage API Abuse"]}}, "lab_environment": {"setup": "Isolated vulnerable web applications", "purpose": "Safe environment for learning exploitation techniques", "features": ["Step-by-step guided exercises", "Complete solution walkthroughs", "Real-world vulnerability scenarios", "Progressive difficulty levels"]}, "current_lab": {"name": "File Inclusion Vulnerabilities", "type": "Add Pages Functionality", "vulnerability_class": "Injection Attacks", "cwe_reference": "CWE-98: Improper Control of Filename for Include/Require Statement", "owasp_category": "A03:2021 – Injection"}, "learning_outcomes": {"knowledge": ["Understanding of file inclusion attack vectors", "Recognition of vulnerable code patterns", "Knowledge of exploitation techniques", "Awareness of security implications"], "skills": ["Ability to identify file inclusion vulnerabilities", "Proficiency in exploitation techniques", "Competence in bypass methods", "Capability to recommend remediation"]}, "instructor_notes": {"teaching_points": ["Emphasize the difference between LFI and RFI", "Demonstrate various bypass techniques", "Show real-world examples and case studies", "Discuss prevention and mitigation strategies"], "common_mistakes": ["Not understanding path traversal concepts", "Overlooking wrapper-based attacks", "Failing to consider log poisoning", "Ignoring extension bypass methods"]}, "resources": {"documentation": "Complete lab documentation available", "solutions": "Step-by-step solutions in answers.html", "support": "Instructor support available during training sessions", "additional_reading": ["OWASP Testing Guide - File Inclusion", "PHP Security Best Practices", "Web Application Hacker's Handbook"]}, "contact_info": {"platform": "cyberwolf.wuaze.com", "support": "Available through training platform", "updates": "Regular content updates and new labs"}}